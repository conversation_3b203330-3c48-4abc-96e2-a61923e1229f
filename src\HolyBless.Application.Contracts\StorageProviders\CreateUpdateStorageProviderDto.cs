using System.ComponentModel.DataAnnotations;
using HolyBless.Enums;

namespace HolyBless.StorageProviders
{
    public class CreateUpdateStorageProviderDto
    {
        [Required]
        public string ProviderName { get; set; } = default!;

        [Required]
        public string ProviderCode { get; set; } = ProviderCodeConstants.CloudFlare;

        public string? PreferCountry { get; set; }
        public string? Description { get; set; }
        public string BindedDomain { get; set; } = "";
    }
}