﻿using HolyBless.Configs;
using HolyBless.Entities.Buckets;
using HolyBless.Enums;
using Microsoft.Extensions.Caching.Memory;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Volo.Abp;
using Volo.Abp.Domain.Repositories;

namespace HolyBless.StorageProviders
{
    public class ReadOnlyStorageProviderAppService : HolyBlessAppService, IReadOnlyStorageProviderAppService
    {
        protected readonly IRepository<StorageProvider, int> _repository;
        protected readonly IMemoryCache _memoryCache;
        protected readonly AppConfig _settings;
        protected static readonly string KeyProvider = "provider_cache";
        protected readonly MemoryCacheEntryOptions _memoryCacheEntryOptions = new()
        {
            AbsoluteExpirationRelativeToNow = TimeSpan.FromMinutes(600),
            SlidingExpiration = TimeSpan.FromMinutes(60),
            Priority = CacheItemPriority.High
        };
        public ReadOnlyStorageProviderAppService(
            IRepository<StorageProvider, int> repository
            , IMemoryCache memoryCache
            , AppConfig settings
            )
        {
            _memoryCache = memoryCache;
            _settings = settings;
            _repository = repository;
        }


        protected async Task<List<StorageProvider>> GetCachedAllProviders()
        {
            if (!_memoryCache.TryGetValue(KeyProvider, out List<StorageProvider>? providers))
            {
                providers = await _repository.GetListAsync();
                if (providers == null || providers.Count == 0)
                {
                    throw new BusinessException("Could not find any storage provider");
                }
                // Cache the result  
                _memoryCache.Set(KeyProvider, providers, _memoryCacheEntryOptions);
            }
            return providers ??[];
        }

        public async Task<StorageProviderDto> GetProvidersByCountry(string preferCountry)
        {
            var allProviders = await GetCachedAllProviders();
            var provider = allProviders.FirstOrDefault(p => p.PreferCountry == preferCountry && p.Environment == _settings.Environment);
            //If could not find prefered country provider, use China's
            if (provider == null)
            {
                if (preferCountry == CountryCodeConstants.HongKong || preferCountry == CountryCodeConstants.Macao)
                {
                    provider = allProviders.FirstOrDefault(p => p.PreferCountry == CountryCodeConstants.China && p.Environment == _settings.Environment);
                }
                else
                {
                    provider = allProviders.FirstOrDefault(p => p.PreferCountry == CountryCodeConstants.UnitedStates && p.Environment == _settings.Environment);
                }
            }
            if (provider == null)
            {
                throw new Volo.Abp.BusinessException("Could not find any storage provider");
            }
            return ObjectMapper.Map<StorageProvider, StorageProviderDto>(provider);
        }

        public async Task<string> GetFileAccessUrlSync(string fileName, string relativePath, string preferCountry)
        {
            Check.NotNullOrWhiteSpace(fileName, nameof(fileName));

            var cacheKey = $"{KeyProvider}_{fileName}_{relativePath}_{preferCountry}";
            if (!_memoryCache.TryGetValue(cacheKey, out string fileAccessUrl))
            {
                // Simulate fetching the URL from a storage provider
                fileAccessUrl = $"https://{preferCountry}.storageprovider.com/{relativePath}/{fileName}";
                // Cache the result
                _memoryCache.Set(cacheKey, fileAccessUrl, _memoryCacheEntryOptions);
            }
            return fileAccessUrl;
        }
    }
}
