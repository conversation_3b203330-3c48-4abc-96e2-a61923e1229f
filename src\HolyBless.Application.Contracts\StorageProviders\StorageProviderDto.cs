using System;
using HolyBless.Enums;
using Volo.Abp.Application.Dtos;

namespace HolyBless.StorageProviders
{
    public class StorageProviderDto : EntityDto<int>
    {
        public string ProviderName { get; set; } = default!;
        public string ProviderCode { get; set; } = ProviderCodeConstants.CloudFlare;
        public string? PreferCountry { get; set; }
        public string? Description { get; set; }
        public string BindedDomain { get; set; } = "";
    }
}