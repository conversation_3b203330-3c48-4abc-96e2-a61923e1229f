using System;
using System.Threading.Tasks;
using HolyBless.Exceptions;
using Shouldly;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Modularity;
using Volo.Abp.Validation;
using Xunit;

namespace HolyBless.StorageProviders
{
    public abstract class StorageProviderAppService_Tests<TStartupModule> : HolyBlessApplicationTestBase<TStartupModule>
    where TStartupModule : IAbpModule
    {
        private readonly IStorageProviderAppService _storageProviderAppService;

        protected StorageProviderAppService_Tests()
        {
            _storageProviderAppService = GetRequiredService<IStorageProviderAppService>();
        }

        [Fact]
        public async Task Should_Get_List_Of_StorageProviders()
        {
            // Act
            var result = await _storageProviderAppService.GetListAsync(new PagedAndSortedResultRequestDto());

            // Assert
            result.TotalCount.ShouldBeGreaterThan(0);
            result.Items.ShouldContain(sp => sp.ProviderName == "CF");
        }

        [Fact]
        public async Task Should_Create_A_New_StorageProvider()
        {
            // Act
            var result = await _storageProviderAppService.CreateAsync(
                new CreateUpdateStorageProviderDto
                {
                    ProviderName = "NewProvider",
                    PreferCountry = "US",
                }
            );

            // Assert
            result.Id.ShouldNotBe(0);
            result.ProviderName.ShouldBe("NewProvider");
        }

        [Fact]
        public async Task Should_Not_Create_A_StorageProvider_Without_ProviderName()
        {
            // Act & Assert
            await Assert.ThrowsAsync<AbpValidationException>(async () =>
            {
                await _storageProviderAppService.CreateAsync(
                    new CreateUpdateStorageProviderDto
                    {
                        PreferCountry = "US",
                    }
                );
            });
        }

        [Fact]
        public async Task Should_Update_Existing_StorageProvider()
        {
            // Arrange
            var storageProvider = await _storageProviderAppService.CreateAsync(
                new CreateUpdateStorageProviderDto
                {
                    ProviderName = "UpdateProvider",
                    PreferCountry = "US",
                }
            );

            // Act
            var result = await _storageProviderAppService.UpdateAsync(
                storageProvider.Id,
                new CreateUpdateStorageProviderDto
                {
                    ProviderName = "UpdatedProvider",
                    PreferCountry = "US",
                }
            );

            // Assert
            result.ProviderName.ShouldBe("UpdatedProvider");
        }

        [Fact]
        public async Task Should_Delete_Existing_StorageProvider()
        {
            // Arrange
            var storageProvider = await _storageProviderAppService.CreateAsync(
                new CreateUpdateStorageProviderDto
                {
                    ProviderName = "DeleteProvider",
                    PreferCountry = "US",
                }
            );

            // Act
            await _storageProviderAppService.DeleteAsync(storageProvider.Id);

            // Assert
            var result = await _storageProviderAppService.GetListAsync(new PagedAndSortedResultRequestDto());
            result.Items.ShouldNotContain(sp => sp.ProviderName == "DeleteProvider");
        }

        [Fact]
        public async Task Should_Get_Provider_By_Country()
        {
            // Arrange
            var storageProvider = await _storageProviderAppService.CreateAsync(
                new CreateUpdateStorageProviderDto
                {
                    ProviderName = "CountryProviderForCA",
                    PreferCountry = "CA",
                }
            );

            // Act
            var result = await _storageProviderAppService.GetProvidersByCountry("CA");
            //Assert
            result.ShouldNotBeNull();
        }

        [Fact]
        public async Task Should_Fallback_To_China_Provider_If_Country_Not_Found()
        {
            // Arrange
            var chinaProvider = await _storageProviderAppService.CreateAsync(
                new CreateUpdateStorageProviderDto
                {
                    ProviderName = "ChinaProvider",
                    PreferCountry = "CN",
                }
            );

            // Act
            var result = await _storageProviderAppService.GetProvidersByCountry("HK");

            // Assert
            result.ShouldNotBeNull();
        }
    }
}