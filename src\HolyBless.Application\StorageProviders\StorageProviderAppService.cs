using System.Collections.Generic;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Threading.Tasks;
using HolyBless.Configs;
using HolyBless.Entities.Buckets;
using HolyBless.Enums;
using HolyBless.Exceptions;
using HolyBless.Permissions;
using Microsoft.AspNetCore.Authorization;
using Microsoft.Extensions.Caching.Memory;
using Volo.Abp;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;
using Volo.Abp.Domain.Repositories;

namespace HolyBless.StorageProviders
{
    [Authorize(HolyBlessPermissions.StorageProviders.Default)]
    [RemoteService(false)]
    public class StorageProviderAppService : ReadOnlyStorageProviderAppService, IStorageProviderAppService
    {

        public StorageProviderAppService(
            IRepository<StorageProvider, int> repository
            , IMemoryCache memoryCache
            , AppConfig settings
            ) : base(repository, memoryCache, settings)
        {
        }

        public async Task<StorageProviderDto> GetAsync(int id)
        {
            var storageProvider = await _repository.GetAsync(id);
            return ObjectMapper.Map<StorageProvider, StorageProviderDto>(storageProvider);
        }

        public async Task<PagedResultDto<StorageProviderDto>> GetListAsync(PagedAndSortedResultRequestDto input)
        {
            var env = _settings.Environment;
            var queryable = await _repository.GetQueryableAsync();
            var query = queryable
                .Where(x => x.Environment == env)
                .OrderBy(input.Sorting ?? "ProviderName")
                .Skip(input.SkipCount)
                .Take(input.MaxResultCount);

            var storageProviders = await AsyncExecuter.ToListAsync(query);
            var totalCount = await AsyncExecuter.CountAsync(queryable);

            return new PagedResultDto<StorageProviderDto>(
                totalCount,
                ObjectMapper.Map<List<StorageProvider>, List<StorageProviderDto>>(storageProviders)
            );
        }

        
        [Authorize(HolyBlessPermissions.StorageProviders.Create)]
        public async Task<StorageProviderDto> CreateAsync(CreateUpdateStorageProviderDto input)
        {
            var storageProvider = ObjectMapper.Map<CreateUpdateStorageProviderDto, StorageProvider>(input);
            storageProvider.Environment = _settings.Environment;
            storageProvider = await _repository.InsertAsync(storageProvider, autoSave: true);
            return ObjectMapper.Map<StorageProvider, StorageProviderDto>(storageProvider);
        }

        [Authorize(HolyBlessPermissions.StorageProviders.Edit)]
        public async Task<StorageProviderDto> UpdateAsync(int id, CreateUpdateStorageProviderDto input)
        {
            var storageProvider = await _repository.GetAsync(id);
            ObjectMapper.Map(input, storageProvider);
            storageProvider = await _repository.UpdateAsync(storageProvider, true);
            return ObjectMapper.Map<StorageProvider, StorageProviderDto>(storageProvider);
        }

        [Authorize(HolyBlessPermissions.StorageProviders.Delete)]
        public async Task DeleteAsync(int id)
        {
            await _repository.DeleteAsync(id, true);
        }
    }
}