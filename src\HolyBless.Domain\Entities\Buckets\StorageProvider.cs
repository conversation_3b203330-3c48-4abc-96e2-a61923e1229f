using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using HolyBless.Enums;
using HolyBless.Lookups;
using Volo.Abp.Domain.Entities.Auditing;

namespace HolyBless.Entities.Buckets
{
    public class StorageProvider : FullAuditedAggregateRoot<int>
    {
        public string ProviderName { get; set; } = default!;
        public string ProviderCode { get; set; } = ProviderCodeConstants.CloudFlare;

        // public string AccessId { get; set; } = default!;
        // public string AccessSecretKey { get; set; } = default!;
        // public string ApiEndPoint { get; set; } = default!;
        public ICollection<Country> PreferCountries { get; set; } = new List<Country>();

        public string? Environment { get; set; }
        //Write comment about Ali express region etc
        public string? Description { get; set; } 
        
        //Binded domain for this storage provider, like r2.holybless.com
        public string BindedDomain { get; set; } = ""; 
        public ICollection<Bucket> Buckets { get; set; } = new List<Bucket>();

        public StorageProvider()
        {
            
        }
        public StorageProvider(int id):base(id)
        {
            
        }
    }
}