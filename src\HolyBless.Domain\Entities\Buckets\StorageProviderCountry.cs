using HolyBless.Lookups;
using Volo.Abp.Domain.Entities;

namespace HolyBless.Entities.Buckets
{
    public class StorageProviderCountry : Entity
    {
        public int StorageProviderId { get; set; }
        public int CountryId { get; set; }
        
        // Navigation properties
        public StorageProvider StorageProvider { get; set; } = default!;
        public Country Country { get; set; } = default!;

        public StorageProviderCountry()
        {
        }

        public StorageProviderCountry(int storageProviderId, int countryId)
        {
            StorageProviderId = storageProviderId;
            CountryId = countryId;
        }

        public override object[] GetKeys()
        {
            return new object[] { StorageProviderId, CountryId };
        }
    }
}
