using System;
using System.Linq;
using System.Threading.Tasks;
using HolyBless.Buckets.Dtos;
using Shouldly;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Modularity;
using Volo.Abp.Validation;
using Xunit;

namespace HolyBless.Buckets
{
    public abstract class BucketAppService_Tests<TStartupModule> : HolyBlessApplicationTestBase<TStartupModule>
            where TStartupModule : IAbpModule
    {
        private readonly IBucketAppService _bucketAppService;

        protected BucketAppService_Tests()
        {
            _bucketAppService = GetRequiredService<IBucketAppService>();
        }

        [Fact]
        public async Task Should_Get_List_Of_Buckets()
        {
            // Act
            var result = await _bucketAppService.GetListAsync(new PagedAndSortedResultRequestDto());

            // Assert
            result.TotalCount.ShouldBeGreaterThan(0);
            result.Items.ShouldContain(b => b.BucketName == "Bucket1");

            var item = result.Items.FirstOrDefault(b => b.BucketName == "Bucket1");
            item.ShouldNotBeNull();
            item.StorageName.ShouldBe("CF");

            var readBucket = await _bucketAppService.GetAsync(item.Id);
            readBucket.ShouldNotBeNull();
        }

        [Fact]
        public async Task Should_Not_Get_List_Of_Buckets_Production()
        {
            // Act
            var result = await _bucketAppService.GetListAsync(new PagedAndSortedResultRequestDto());

            // Assert
            // Shouldn't contains Buckets from production provider
            result.Items.ShouldNotContain(b => b.BucketName == "Bucket2");
        }

        [Fact]
        public async Task Should_Create_A_New_Bucket()
        {
            // Act
            var providerId = await GetOneValidProviderId();
            var result = await _bucketAppService.CreateAsync(
                new CreateUpdateBucketDto
                {
                    BucketName = "NewBucket",
                    StorageProviderId = providerId,
                    LanguageCode = "US",
                    SubDomain = "https://bucketNew.com",
                }
            );

            // Assert
            result.Id.ShouldNotBe(0);
            result.BucketName.ShouldBe("NewBucket");
        }

        [Fact]
        public async Task Should_Not_Create_A_Bucket_Without_Name()
        {
            // Act & Assert
            await Assert.ThrowsAsync<AbpValidationException>(async () =>
            {
                await _bucketAppService.CreateAsync(
                    new CreateUpdateBucketDto
                    {
                        BucketName = "",
                        StorageProviderId = 1000,
                        LanguageCode = "US",
                        SubDomain = "https://bucketNew.com",
                    }
                );
            });
        }

        [Fact]
        public async Task Should_Update_Existing_Bucket()
        {
            var providerId = await GetOneValidProviderId();
            // Arrange
            var bucket = await _bucketAppService.CreateAsync(
                new CreateUpdateBucketDto
                {
                    BucketName = "UpdateBucket",
                    StorageProviderId = providerId,
                    LanguageCode = "US",
                    SubDomain = "https://bucketUpdate.com",
                }
            );

            // Act
            var result = await _bucketAppService.UpdateAsync(
                bucket.Id,
                new CreateUpdateBucketDto
                {
                    BucketName = "UpdatedBucket",
                    StorageProviderId = providerId,
                    LanguageCode = "US",
                    SubDomain = "https://bucketUpdate.com",
                }
            );

            // Assert
            result.BucketName.ShouldBe("UpdatedBucket");
        }

        [Fact]
        public async Task Should_Delete_Existing_Bucket()
        {
            var providerId = await GetOneValidProviderId();
            // Arrange
            var bucket = await _bucketAppService.CreateAsync(
                new CreateUpdateBucketDto
                {
                    BucketName = "DeleteBucket",
                    StorageProviderId = providerId,
                    LanguageCode = "US",
                    SubDomain = "https://bucketDelete.com",
                }
            );

            // Act
            await _bucketAppService.DeleteAsync(bucket.Id);

            // Assert
            var result = await _bucketAppService.GetListAsync(new PagedAndSortedResultRequestDto());
            result.Items.ShouldNotContain(b => b.BucketName == "DeleteBucket");
        }

        [Fact]
        public async Task Should_Get_List_Of_Buckets_By_ProviderId()
        {
            // Arrange
            var providerId = await GetOneValidProviderId();

            // Act
            var result = await _bucketAppService.GetListByProviderIdAysnc(providerId);

            // Assert
            result.ShouldNotBeNull();
            result.ShouldAllBe(b => b.StorageProviderId == providerId);
            result.Count.ShouldBeGreaterThan(0);
        }

        private async Task<int> GetOneValidProviderId()
        {
            var existings = await _bucketAppService.GetListAsync(new PagedAndSortedResultRequestDto());
            var providerId = existings.Items.FirstOrDefault()?.StorageProviderId;
            return providerId ?? 0;
        }
    }
}