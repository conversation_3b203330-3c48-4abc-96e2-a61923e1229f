using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using HolyBless.Enums;
using Volo.Abp.Domain.Entities.Auditing;

namespace HolyBless.Lookups
{
    public class Country : AuditedAggregateRoot<int>
    {
        public string Name { get; set; } = "";
        public string Code3 { get; set; } = "";
        public string Code { get; set; } = "";

        public string DefaultLangCode { get; set; } = LangCode.English;
        public string DefaultSpokenLangCode { get; set; } = SpokenLangCode.English;

        // Foreign key to StorageProvider (optional - a country may not have a storage provider)
        public int? StorageProviderId { get; set; }

        public Country()
        {
        }

        public Country(string name, string code3, string code)
        {
            Name = name;
            Code3 = code3;
            Code = code;
        }

        public Country(string name, string code3, string code, string langCode, string spokenLangCode) :
            this(name, code3, code)
        {
            DefaultLangCode = langCode;
            DefaultSpokenLangCode = spokenLangCode;
        }
    }
}