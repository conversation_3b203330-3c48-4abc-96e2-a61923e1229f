using HolyBless.Enums;
using Volo.Abp.Application.Dtos;

namespace HolyBless.Buckets.Dtos
{
    public class BucketDto : EntityDto<int>
    {
        public int StorageProviderId { get; set; }
        public string StorageName { get; set; } = default!;
        public string BucketName { get; set; } = default!;
        public string LanguageCode { get; set; } = default!;
        public string SubDomain { get; set; } = default!; //Bucket Sub Domain Url
        public ContentCategory ContentType { get; set; }
    }
}