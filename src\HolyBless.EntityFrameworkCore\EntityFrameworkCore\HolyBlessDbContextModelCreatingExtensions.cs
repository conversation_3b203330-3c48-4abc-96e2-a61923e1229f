﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection.Emit;
using System.Text;
using System.Threading.Tasks;
using HolyBless.Entities.Articles;
using HolyBless.Entities.Books;
using HolyBless.Entities.Buckets;
using HolyBless.Entities.Channels;
using HolyBless.Entities.Collections;
using HolyBless.Entities.Tags;
using HolyBless.Entities.VirtualFolders;
using HolyBless.Lookups;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Volo.Abp.EntityFrameworkCore.Modeling;

namespace HolyBless.EntityFrameworkCore
{
    public static class HolyBlessDbContextModelCreatingExtensions
    {
        private static readonly int CodeLength = 50;
        private static readonly int NameLength = 100;
        private static readonly int UrlLength = 256;
        private static readonly int TitleLength = 100;
        private static readonly int DescriptionLength = 256;
        private static readonly int KeywordLength = 128;
        private static readonly int LanguageCodeLength = 3;

        public static void ConfigureDomainModels(this ModelBuilder builder)
        {
            var truncateSecondsConverter = new ValueConverter<DateTime, DateTime>(
            v => TruncateToMinute(v),  // Convert before saving to database
            v => v                     // No change when reading
            );

            builder.Entity<EBook>(b =>
            {
                b.ConfigureByConvention(); //auto configure for the base class props
                b.ConfigId().ConfigLanguageCode();
                b.ConfigTitle();
            });

            builder.Entity<Series>(b =>
            {
                b.ConfigureByConvention();
                b.ConfigId().ConfigLanguageCode();
                b.ConfigTitle().ConfigDescription();

                b.HasOne(s => s.Channel)
                    .WithMany()
                    .HasForeignKey(s => s.ChannelId)
                    .OnDelete(DeleteBehavior.SetNull);

                b.HasOne(s => s.ThumbnailBucketFile)
                    .WithMany()
                    .HasForeignKey(s => s.ThumbnailFileId)
                    .OnDelete(DeleteBehavior.SetNull);
            });

            builder.Entity<StorageProvider>(e =>
            {
                e.ConfigureByConvention();
                e.ConfigId();
                e.Property(x => x.ProviderName).IsRequired().HasMaxLength(NameLength).IsUnicode(false);
                e.Property(x => x.ProviderCode).IsRequired().HasMaxLength(10).IsUnicode(false);
                // e.Property(x => x.AccessId).IsRequired().HasMaxLength(UrlLength).IsUnicode(false);
                // e.Property(x => x.AccessSecretKey).IsRequired().HasMaxLength(UrlLength).IsUnicode(false);
                // e.Property(x => x.ApiEndPoint).IsRequired().HasMaxLength(UrlLength).IsUnicode(false);
                e.Property(x => x.PreferCountry).HasMaxLength(LanguageCodeLength).IsUnicode(false);
                e.Property(x => x.Description).HasMaxLength(DescriptionLength).IsUnicode(true);
                e.Property(x => x.Environment).HasMaxLength(20).IsUnicode(false);
                e.Property(x => x.BindedDomain).IsRequired().HasMaxLength(50).IsUnicode(false);
                e.HasMany(x => x.Buckets).WithOne(p => p.StorageProvider).HasForeignKey(x => x.StorageProviderId);
            });

            builder.Entity<Bucket>(e =>
            {
                e.ConfigureByConvention();
                e.ConfigId().ConfigLanguageCode();
                e.Property(x => x.SubDomain).IsRequired().HasMaxLength(UrlLength).IsUnicode(false);
                e.Property(x => x.BucketName).IsRequired().HasMaxLength(NameLength).IsUnicode(true);

                e.HasOne(x => x.StorageProvider).WithMany(p => p.Buckets).HasForeignKey(x => x.StorageProviderId);
            });
            builder.Entity<BucketFile>(e =>
            {
                e.ConfigureByConvention();
                e.ConfigId().ConfigTitle().ConfigLanguageCode();
                e.Property(x => x.FileName).IsRequired().HasMaxLength(NameLength).IsUnicode(true);
                e.Property(x => x.RelativePathInBucket).IsRequired().HasMaxLength(UrlLength).IsUnicode(true);
                e.Property(x => x.YoutubeId).HasMaxLength(UrlLength).IsUnicode(false);
            });

            builder.Entity<BucketToFile>(e =>
            {
                e.HasKey(x => new { x.BucketId, x.FileId });
                e.HasOne(x => x.Bucket).WithMany().HasForeignKey(x => x.BucketId).OnDelete(DeleteBehavior.NoAction);
                e.HasOne(x => x.BucketFile).WithMany().HasForeignKey(x => x.FileId).OnDelete(DeleteBehavior.NoAction);
            });

            builder.Entity<VirtualDiskFolder>(e =>
            {
                e.ConfigureByConvention();
                e.ConfigId().ConfigLanguageCode();
                e.Property(x => x.FolderName).IsRequired().HasMaxLength(NameLength);

                e.HasOne(x => x.ContentCode).WithMany().HasForeignKey(x => x.ContentCodeId);
                e.HasOne(x => x.ParentFolder).WithMany().HasForeignKey(x => x.ParentFolderId);
                e.HasOne(x => x.Channel).WithMany(c => c.VirtualDiskFolders).HasForeignKey(x => x.ChannelId);
                e.HasMany(x => x.Children).WithOne(x => x.ParentFolder).HasForeignKey(x => x.ParentFolderId);
            });

            builder.Entity<StagingFolder>(e =>
            {
                e.ConfigureByConvention();
                e.ConfigId().ConfigLanguageCode();
                e.Property(x => x.FolderName).IsRequired().HasMaxLength(NameLength);
                e.Property(x => x.FolderCode).IsRequired().HasMaxLength(CodeLength).IsUnicode(false);

                e.HasOne(x => x.ParentFolder).WithMany().HasForeignKey(x => x.ParentFolderId);
                e.HasMany(x => x.Children).WithOne(x => x.ParentFolder).HasForeignKey(x => x.ParentFolderId);
            });

            builder.Entity<FolderToBucketFile>(e =>
            {
                e.HasKey(x => new { x.FolderId, x.BucketFileId });
                e.HasOne(x => x.Folder).WithMany(f => f.FolderToBucketFiles).HasForeignKey(x => x.FolderId).OnDelete(DeleteBehavior.NoAction);
                e.HasOne(x => x.BucketFile).WithMany(f => f.FolderToBucketFiles).HasForeignKey(x => x.BucketFileId).OnDelete(DeleteBehavior.NoAction);
            });

            builder.Entity<VirtualDiskFolderTree>(e =>
            {
                e.Property(x => x.TreeJsonData).IsRequired().HasColumnType("text").IsUnicode(true);
            });
            builder.Entity<Channel>(e =>
            {
                e.ConfigureByConvention();
                e.ConfigId().ConfigLanguageCode();
                e.Property(x => x.ContentCode).IsRequired().HasMaxLength(CodeLength).IsUnicode(false);
                e.Property(x => x.Name).IsRequired().HasMaxLength(NameLength).IsUnicode(true);

                e.HasOne(x => x.ParentChannel).WithMany(p => p.ChildChannels).HasForeignKey(x => x.ParentChannelId);
                e.HasOne(c => c.Collection).WithOne(c => c.Channel).HasForeignKey<Collection>(c => c.ChannelId);
            });

            builder.Entity<Article>(e =>
            {
                e.ConfigureByConvention();
                e.ConfigId().ConfigLanguageCode().ConfigTitle().ConfigDescription();
                e.Property(x => x.Keywords).HasMaxLength(KeywordLength).IsUnicode(true);
                e.Property(x => x.Content).IsUnicode(true).HasColumnType("text");
                e.Property(x => x.Memo).HasMaxLength(DescriptionLength).IsUnicode(true);

                e.Property(x => x.DeliveryDate).HasConversion(truncateSecondsConverter);

                e.HasOne(x => x.ThumbnailBucketFile).WithMany().HasForeignKey(x => x.ThumbnailFileId);
                e.HasOne(x => x.VirtualDiskFolder).WithOne(f => f.Article).HasForeignKey<VirtualDiskFolder>(f => f.ArticleId);
                e.HasMany(x => x.ArticleFiles).WithOne(af => af.Article).HasForeignKey(af => af.ArticleId);
            });

            builder.Entity<ArticleFile>(e =>
            {
                e.ConfigureByConvention();
                e.ConfigId().ConfigTitle().ConfigDescription();

                e.HasOne(x => x.Article).WithMany(a => a.ArticleFiles).HasForeignKey(x => x.ArticleId).OnDelete(DeleteBehavior.Cascade);
                e.HasOne(x => x.BucketFile).WithMany().HasForeignKey(x => x.FileId).OnDelete(DeleteBehavior.NoAction);

                e.HasIndex(x => new { x.ArticleId, x.FileId }).IsUnique();
                e.HasIndex(x => new { x.ArticleId, x.IsPrimary });
            });

            builder.Entity<Collection>(e =>
            {
                e.ConfigureByConvention();
                e.ConfigId().ConfigLanguageCode().ConfigDescription();
                e.Property(x => x.ContentCode).IsRequired().HasMaxLength(CodeLength).IsUnicode(false);

                e.Property(x => x.Name).IsRequired().HasMaxLength(NameLength).IsUnicode(true);
                e.Property(x => x.Keywords).HasMaxLength(KeywordLength).IsUnicode(true);
                e.Property(x => x.Memo).HasMaxLength(DescriptionLength).IsUnicode(true);
                e.Property(x => x.Memo).IsUnicode(true).HasColumnType("text");

                e.HasOne(x => x.ParentCollection).WithMany().HasForeignKey(x => x.ParentCollectionId);
                e.HasOne(x => x.ThumbnailBucketFile).WithMany().HasForeignKey(x => x.ThumbnailFileId);
            });

            builder.Entity<CollectionToArticle>(e =>
            {
                e.HasKey(x => new { x.CollectionId, x.ArticleId });
                e.HasOne(x => x.Collection).WithMany(c => c.CollectionToArticles).HasForeignKey(x => x.CollectionId).OnDelete(DeleteBehavior.NoAction);
                e.HasOne(x => x.Article).WithMany(a => a.CollectionToArticles).HasForeignKey(x => x.ArticleId).OnDelete(DeleteBehavior.NoAction);
            });

            builder.Entity<CollectionToFile>(e =>
            {
                e.HasKey(x => new { x.CollectionId, x.FileId });
                e.HasOne(x => x.Collection).WithMany(c => c.CollectionToFiles).HasForeignKey(x => x.CollectionId).OnDelete(DeleteBehavior.NoAction);
                e.HasOne(x => x.BucketFile).WithMany(f => f.CollectionToFiles).HasForeignKey(x => x.FileId).OnDelete(DeleteBehavior.NoAction);
            });
            builder.Entity<TeacherArticleLink>(e =>
            {
                e.HasKey(x => new { x.StudentArticleId, x.TeacherArticleId });
                e.HasOne(x => x.StudentArticle).WithMany().HasForeignKey(x => x.StudentArticleId).OnDelete(DeleteBehavior.NoAction);
                e.HasOne(x => x.TeacherArticle).WithMany().HasForeignKey(x => x.TeacherArticleId).OnDelete(DeleteBehavior.NoAction);
            });

            builder.Entity<Tag>(e =>
            {
                e.ConfigureByConvention();
                e.ConfigId().ConfigLanguageCode();
                e.Property(x => x.ContentCode).IsRequired().HasMaxLength(CodeLength).IsUnicode(false);

                e.Property(x => x.TagName).IsRequired().HasMaxLength(NameLength).IsUnicode(true);
            });

            builder.Entity<ArticleToTag>(e =>
            {
                e.ConfigureByConvention();
                e.HasKey(x => new { x.TagId, x.ArticleId });
                e.HasOne(x => x.Article).WithMany(a => a.ArticleToTags).HasForeignKey(x => x.ArticleId).OnDelete(DeleteBehavior.NoAction);
                e.HasOne(x => x.Tag).WithMany(t => t.ArticleToTags).HasForeignKey(x => x.TagId).OnDelete(DeleteBehavior.NoAction);
            });

            //Lookup Tables
            builder.Entity<ContentCode>(e =>
            {
                e.ConfigureByConvention();
                e.ConfigId();
                e.Property(x => x.Code).IsRequired().HasMaxLength(CodeLength).IsUnicode(false);
                e.Property(x => x.Description).IsRequired().HasMaxLength(DescriptionLength).IsUnicode(true);
            });
            builder.Entity<Country>(e =>
            {
                e.ConfigureByConvention();
                e.ConfigId();
                e.Property(x => x.Code).IsRequired().HasMaxLength(2).IsUnicode(false);
                e.Property(x => x.Name).IsRequired().HasMaxLength(50).IsUnicode(true);
                e.Property(x => x.Code3).IsRequired().HasMaxLength(3).IsUnicode(false);
            });
        }

        private static EntityTypeBuilder ConfigId(this EntityTypeBuilder entity)
        {
            entity.Property("Id").ValueGeneratedOnAdd().UseIdentityByDefaultColumn();
            return entity;
        }

        private static EntityTypeBuilder ConfigLanguageCode(this EntityTypeBuilder entity)
        {
            entity.Property("LanguageCode").HasMaxLength(10).IsUnicode(false);
            return entity;
        }

        private static EntityTypeBuilder ConfigTitle(this EntityTypeBuilder entity)
        {
            entity.Property("Title").HasMaxLength(TitleLength).IsUnicode(true);
            return entity;
        }

        private static EntityTypeBuilder ConfigDescription(this EntityTypeBuilder entity)
        {
            entity.Property("Description").HasMaxLength(DescriptionLength).IsUnicode(true);
            return entity;
        }

        private static EntityTypeBuilder ConfigLongDescription(this EntityTypeBuilder entity)
        {
            entity.Property("Description").HasColumnType("text").IsUnicode(true);
            return entity;
        }

        private static DateTime TruncateToMinute(DateTime dt)
        {
            return new DateTime(dt.Year, dt.Month, dt.Day, dt.Hour, dt.Minute, 0, 0, dt.Kind);
        }
    }
}